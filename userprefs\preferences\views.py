from django.shortcuts import render, redirect
from django.http import HttpResponse

def home(request):
    theme = request.COOKIES.get("theme", "light")  # default light
    language = request.COOKIES.get("language", "en")  # default English
    return render(request, "preferences/home.html", {"theme": theme, "language": language})

def set_preferences(request):
    if request.method == "POST":
        theme = request.POST.get("theme", "light")
        language = request.POST.get("language", "en")

        response = redirect("home")
        # Set cookies with expiry (7 days)
        response.set_cookie("theme", theme, max_age=7*24*60*60, secure=True, httponly=False, samesite="Lax")
        response.set_cookie("language", language, max_age=7*24*60*60, secure=True, httponly=False, samesite="Lax")
        return response

    return render(request, "preferences/set_preferences.html")
